﻿### Parameters
#
param (
    [Parameter()]
    [switch]$prmSilent
)


Write-Host "## Update ERP exports"
& .\UpdateViews.ps1 -prmSilent -prmUpdates @("ERP")
Write-Host "## Update Master Spreadsheet"
& .\UpdateMaster.ps1 -prmSilent
Write-Host "## Update RFC Manager Spreadsheets"
& .\UpdateProjects.ps1 -prmScript
Write-Host "## Update Views"
& .\UpdateViews.ps1 -prmSilent -prmUpdates @("EVAL")

if (-not $prmSilent.IsPresent)
{
    Pause
}
