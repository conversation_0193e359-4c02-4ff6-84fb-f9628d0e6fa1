### Parameters
#
param (
	[Parameter()]
    [switch]$prmSilent,
    $prmUpdates
)

$VerbosePreference = "Continue" # Ensures verbose messages are displayed and logged

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$GlobalScriptParameters = Get-SetupParameters
$ReportYear = $GlobalScriptParameters.Year
$SharePointTeamViewPath = $GlobalScriptParameters.SharePointMasterTeamView
$SharePointRFCsPath = $GlobalScriptParameters.SharePointRFCsPath
$SharePointRosterViewPath = $GlobalScriptParameters.SharePointRosterView
$MasterVsBudgetFilename = $GlobalScriptParameters.MasterVsBudgetFilename
$SharePointMasterFolder = $GlobalScriptParameters.SharePointMasterFolder
$MasterVsBudgetPath = $SharePointMasterFolder + $MasterVsBudgetFilename
$SharePointWorkloadPath = $GlobalScriptParameters.SharePointWorkload
$SharePointDirectCostPath = $GlobalScriptParameters.SharePointDirectCost
# Filename of Master file (should be in script folder)
$MasterFilename = $GlobalScriptParameters.MasterFilename
$MasterSharepointPath = $SharePointMasterFolder + $MasterFilename


# Start Excel
$ExcelHndl = Start-Excel

function Update-ViewFile
{
	param(
		[Parameter(Mandatory=$true)]
		[string] $FilePath
	)

	$StartTime = Start-Timer

	$WorkbookHndl = Open-Workbook -excelHndl $ExcelHndl -workbookFilePath $FilePath -isSharepoint $true
	$Workbook = $WorkbookHndl["workbook"]

	Update-AllQueries -WorkbookPrm $Workbook
	Wait-OnAllQueries -WorkbookPrm $Workbook
	Write-Verbose "Update table ALL DONE"

	Close-Workbook -workbookHndl $WorkbookHndl
	Write-Verbose "Workbook closed"
	
	Stop-Timer -StartTime $StartTime -OperationName $FilePath
}

function Get-ProjectLeadFiles
{
	# Query the names of all project leads and rfc managers from master file
	$projectLeads = Get-LeadNames -ExcelHndl $ExcelHndl -MasterFilepath $MasterSharepointPath

	$filenames = @()
	foreach ($lead in $projectLeads)
	{
		$leadFilename = Get-ProjectLeadFilename -Name $lead -Year $ReportYear
		$filenames += $SharePointRFCsPath + "$($leadFilename).xlsx"
	}
	return $filenames
}

try{
	# If no update modes are passed, we update everything
	# If update modes are passed, they will be used
	# If no update modes are passed and this is not a silent call, ask again
	$prmUpdatesAll = @("ERP", "MASTER", "EVAL", "LEADS")
	$SelectedUpdates = $prmUpdatesAll
	if($null -ne $prmUpdates){
		$SelectedUpdates = $prmUpdates
	}
	elseif(-not $prmSilent.IsPresent){
		$SelectedUpdates = Select-ByGUIWithOptions  -CandidateList $prmUpdatesAll -OptionsPrm @{"SelectionMode"="MultiExtended"}
	}
	
	if ("ERP" -in $SelectedUpdates)
	{
		Update-ViewFile -FilePath $SharePointWorkloadPath
		Update-ViewFile -FilePath $SharePointDirectCostPath
	}
	if ("MASTER" -in $SelectedUpdates)
	{
		Update-ViewFile -FilePath $MasterSharePointPath	
	}
	if ("EVAL" -in $SelectedUpdates)
	{
		Update-ViewFile -FilePath $MasterVsBudgetPath	
		Update-ViewFile -FilePath $SharePointTeamViewPath
		Update-ViewFile -FilePath $SharePointRosterViewPath
	}
	if ("LEADS" -in $SelectedUpdates)
	{
		foreach($filename in Get-ProjectLeadFiles)
		{
			Update-ViewFile -FilePath $filename
		}
	}
}
catch{
	Write-Verbose "An error occurred: $_"
	Write-Verbose "File: $($_.InvocationInfo.ScriptName)"
	Write-Verbose "Line: $($_.InvocationInfo.ScriptLineNumber)"
}
finally{
	Stop-Excel -excelHndl $ExcelHndl
}

if (-not $prmSilent.IsPresent)
{
	Pause
}
