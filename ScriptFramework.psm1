function Run-SubScripts {
    param (
        [string[]]$SubScripts,
        [int]$TimeoutInSeconds,
        [string]$LogFilePath
    )

    # Check if a transcript is already running
    $transcriptRunning = ($null -ne (Get-TranscriptState | Where-Object { $_.Started }))

    if (-not $transcriptRunning) {
        Start-Transcript -Path $LogFilePath -Append
        $transcriptStarted = $true
    } else {
        $transcriptStarted = $false
    }

    try {
        Write-Output "Script started."

        # Execute each subscript
        foreach ($subScript in $SubScripts) {
            try {
                Write-Output "Executing subscript: $subScript"

                # Start the subscript as a job
                $job = Start-Job -ScriptBlock {
                    param($scriptPath)
                    & $scriptPath *>&1
                } -ArgumentList $subScript

                # Wait for the job to finish or timeout
                if (Wait-Job -Job $job -Timeout $TimeoutInSeconds) {
                    $output = Receive-Job -Job $job
                    Write-Output "Subscript completed successfully:`n$output"
                } else {
                    Write-Error "Subscript $subScript timed out and was stopped."
                    Stop-Job -Job $job | Out-Null
                    throw "Subscript $subScript execution exceeded the timeout of $TimeoutInSeconds seconds."
                }
            } catch {
                Write-Error "An error occurred while executing subscript $subScript: $_"
            }
        }
    } catch {
        Write-Error "An error occurred during execution: $_"
    } finally {
        Write-Output "Script finished."

        # Stop the transcript if this script started it
        if ($transcriptStarted) {
            Stop-Transcript
        }
    }
}
