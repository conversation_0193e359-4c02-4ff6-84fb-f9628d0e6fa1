﻿### Parameters
#
$VerbosePreference = "Continue" # Ensures verbose messages are displayed and logged

param (
	[Parameter()]
    [switch]$prmSilent
)

# Import custom modules
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GlobalParameters.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\GUI.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\Utils.psm1") -Force
Import-Module -Name (Join-Path $PSScriptRoot "ScriptModules\ExcelCom.psm1") -Force

$GlobalScriptParameters = Get-SetupParameters
# Line number in which the RFC workbook sheet "Properties" holds the Budget Master filepath
$RFCFilesPath = $GlobalScriptParameters.RelativeRFCFilesPath
# Filename of Master file (should be in script folder)
$MasterFilename = $GlobalScriptParameters.MasterFilename
$MasterSharepointFolder = $GlobalScriptParameters.SharePointMasterFolder
$MasterSharepointPath = $MasterSharepointFolder + $MasterFilename
# Employees roster file
$RosterPath = $GlobalScriptParameters.RelativeRosterPath
# Sharepoint domain (only used for resetting parameter when complete)
$SharePointDomain = $GlobalScriptParameters.SharePointDomain
# Sharepoint projects path (only used for resetting parameter when complete)
$SharePointProjectsPath = $GlobalScriptParameters.SharePointRFCsPath
# Sharepoint employee roster path (only used for resetting parameter when complete)
$SharePointRosterPath = $GlobalScriptParameters.SharePointRosterPath

$DelayedUpdateStageA = @("WorkloadMaster", "DirectCostMaster")
$DelayedUpdateStageB = @("UpdateModeTab","FilePaths")
$DelayedUpdates = $DelayedUpdateStageA + $DelayedUpdateStageB

# Start Excel
$BeforeExcel = Get-Date
$Excel = New-Object -COM "Excel.Application"
$AfterExcel = Get-Date
$ReactivateAutoSave = $false;

try{
	# Compute full file paths
	$RFCFilesPath = $SharePointProjectsPath
	$MasterFilePath = $MasterSharepointPath
	$RosterPath = $SharePointRosterPath

	$Excel.Visible = $false
	$Excel.DisplayAlerts = $false

	Write-Output "Opening workbook $MasterFilePath"
	$Workbook = $Excel.Workbooks.Open($MasterFilePath)
	if ($Workbook.AutoSaveOn) { 
		$Workbook.AutoSaveOn = $false 
		$ReactivateAutoSave = $true
	}
    $Workbook.Queries.FastCombine = $true
    
	$PropertySheet = $Workbook.Sheets.item("Parameters")
	$FilePathTab = $PropertySheet.ListObjects["FilePaths"].Range

	function Set-Cell
	{	param($prmValue, $prmAddress)
		$PropertySheet.Range($prmAddress).Cells(1,2) = $prmValue
	}
	function Get-Cell
	{	param($prmAddress)
		$PropertySheet.Range($prmAddress).Cells(1,2).Value2
	}
	
	# Separate try-block as this is the most likely point of failure
	try{

		# Query update mode from user and then write it into the respective cell in the UpdateModeTab
		$SelectedUpdateMode = "Override"
		if (-not $prmSilent.IsPresent)
		{
			$SelectedUpdateLabels = @{
				"Regular (Update Plan and do not change Master)"="Regular";
				"Override (Update Plan and overwrite Master)"="Override"
			}
			$SelectedUpdateLabel = Select-ByGUIWithOptions `
			  -CandidateList $SelectedUpdateLabels.Keys `
			  -OptionsPrm @{"Preselected"="Regular (Update Plan and do not change Master)"}
			$SelectedUpdateMode = $SelectedUpdateLabels[$SelectedUpdateLabel]		
		}
		$UpdateModeTab = $PropertySheet.ListObjects["UpdateModeTab"].Range
		$UpdateModeAddress = $UpdateModeTab.Find("Master").Address(0,0,1,1)
		Set-Cell -prmValue $SelectedUpdateMode -prmAddress $UpdateModeAddress
		Write-Host "Update mode set to: $(Get-Cell -prmAddress $UpdateModeAddress)"

		
		# Entries are (<Parameters table entry name>, <Parameters table entry address>, 
		# <target path>, <backup path>)
		$AddressMap = @{} 
		foreach($k in @( 
					@("ProjectsPath", $RFCFilesPath, $SharePointProjectsPath), 
					@("EmployeeRoster", $RosterPath, $SharePointRosterPath),
					@("SharepointDomain", $SharePointDomain, $SharePointDomain)
				) ) 
		{
			$address = $FilePathTab.Find($k[0]).Address(0,0,1,1)
			$presetPath = Get-Cell -prmAddress $address
			$backupPath = $k[2]
			if ($presetPath -ne $backupPath){
				Write-Host "Warning: Preset folder is not equal to target sharepoint folder"`
				  -ForegroundColor blue -BackgroundColor white
			}
			$AddressMap.add($k[0], @($k[0], $address, $backupPath) )
			Set-Cell -prmValue $k[1] -prmAddress $address
			Write-Host "Set $($k[0]) path to $($k[1])"
		}

		Update-AllQueries -WorkbookPrm $Workbook -ExceptionListPrm $DelayedUpdates
		Update-AllQueries -WorkbookPrm $Workbook -WhiteListPrm $DelayedUpdateStageA
		Update-AllQueries -WorkbookPrm $Workbook -WhiteListPrm $DelayedUpdateStageB
		Wait-OnAllQueries -WorkbookPrm $Workbook		
		Write-Host "Update table ALL DONE"				
	}
	finally{}
	#catch{
	#	Write-Error "Error occured: $Error[0]"
	#}

	if ($ReactivateAutoSave) { $Workbook.AutoSaveOn = $true }			
	$Workbook.Save()
	$Workbook.Close()
}
#catch{
#	Write-Error "Error occured: $Error[0]"
#}
finally
{
	# Properly close excel COM app
	$Excel.Workbooks | ForEach-Object {$_.Close()}
	$Excel.Quit()

	# Execute garbage collection to ensure correct deallocation of com objects
	[System.GC]::Collect()
	[System.GC]::WaitForPendingFinalizers()

	# Kill the remaining ghost excel process (if it exists)
	Get-Process excel |
	  Where {$BeforeExcel -le $_.StartTime -and $_.StartTime -le $AfterExcel} | Stop-Process
	
	Write-Output "Tidy Up Complete"
}

if (-not $prmSilent.IsPresent)
{
	Pause
}

