﻿### Parameters
#
param (
	[Parameter()]
    [switch]$prmScript
)


Import-Module -Name (Join-Path ((Resolve-Path -Path ".").Path) ".\GlobalParameters.psm1") -Force
$GlobalScriptParameters = Get-GlobalScriptParameters

# Line number in which the RFC workbook sheet "Properties" holds the Budget Master filepath
$RFCFilesPath = $GlobalScriptParameters.RelativeRFCFilesPath
# Filename of Master file (should be in script folder)
$MasterFilePath = ".\" + $GlobalScriptParameters.MasterFilename
# Remote filepath to be set for master and budget file
$SharePointFolder = $GlobalScriptParameters.SharePointMasterFolder

# List of table names which will NOT be subjected to the update based on the update mode
$UpdateExceptions = @{
	"Default"=@("WorkloadPlan","DirectCostPlan");
	"Override"=@();
	"Mirror"=@()
}

# Import modules
Import-Module -Name (Join-Path ((Resolve-Path -Path ".").Path) "ScriptModules\GUI.psm1")`
  -Force
Import-Module -Name (Join-Path ((Resolve-Path -Path ".").Path) "ScriptModules\Utils.psm1")`
  -Force

### Global Functions
#

# Update all queries in given workbook
function Update-Queries
{ 
	param($WorkbookPrm, $UpdateExceptionsPrm)

	Update-AllQueries -WorkbookPrm $WorkbookPrm -ExceptionListPrm $UpdateExceptionsPrm

	Wait-OnAllQueries -WorkbookPrm $WorkbookPrm
}


function Update-Workbook([ref]$ExcelPrm, $RFCFilePathPrm, $UpdateModePrm)
{
    # Entries are (<MasterProps table entry name>, <MasterProps table entry address>, 
    # <target path>, <backup path>)
    $AddressMap = @{} 
    try{
    	# Access workbook and properties sheet
    	Write-Host "Opening workbook $RFCFilePathPrm"
    	$Workbook = $ExcelPrm.Value.Workbooks.Open($RFCFilePathPrm)
    	$Workbook.AutoSaveOn = $false
		
        $Workbook.Queries.FastCombine = $true
    	$PropertySheet = $Workbook.Sheets.item("Properties")
    	$MasterPropsTab = $PropertySheet.ListObjects["MasterProps"].Range

    	# Functions to get and set values in master properties table
    	function Set-Cell {	param($prmValue, $prmAddress)
    		$PropertySheet.Range($prmAddress).Cells(1,2) = $prmValue
    	}
    	function Get-Cell {	param($prmAddress)
    		$PropertySheet.Range($prmAddress).Cells(1,2).Value2
    	}

        try{
			# We change the values of these cells to the given "Value" and
			# after update these are changed back to their former value
			# or the "Restore to" value (if it is set)
			$CellManipulators =
			@(
				@{
					"Name" = "Master File Path";
					"Value" = (Resolve-Path -Path $MasterFilePath).Path
					"Restore to" = $SharePointFolder + (Split-Path `
					  (Resolve-Path -Path $MasterFilePath).Path -leaf)
				}
			)

			if ($UpdateModePrm -ne "Default"){
				$CellManipulators +=
				@{
					"Name" = "Update Mode";
					"Value" = $UpdateModePrm;
				}
			}

			foreach($CellManipulator in $CellManipulators)
			{
				$Name = $CellManipulator["Name"]

				# Insert cell address into CellManipulator
				$Address = $MasterPropsTab.Find($Name).Address(0,0,1,0)
				$CellManipulator.Add("Address", $Address)

				# Insert or validate restore value into CellManipulator
				$OldValue = Get-Cell -prmAddress $Address
				if ("Restore to" -notin $CellManipulator.Keys){
					$CellManipulator.Add("Restore to", $OldValue)
				}
				# Give a message if restore to value is not the same as old value
				elseif($OldValue -ne $CellManipulator["Restore to"]){
					Write-Host "Found value in cell ${Name}:`n$OldValue"
					$RestoreTo = $CellManipulator["Restore to"]
					Write-Host "Will restore to:`n$RestoreTo"
				}

				# Actually write to the cell in the Excel sheet 
				$CellTargetValue = $CellManipulator["Value"]
				Set-Cell -prmValue $CellTargetValue -prmAddress $Address
        		Write-Host "Set $Name path to $CellTargetValue"
			}

			if ($UpdateModePrm -notin $UpdateExceptions.Keys){
				throw "The value $UpdateModePrm is not a valid update mode!"
			}

			Write-Host "Perform update with mode $UpdateModePrm"
			Update-Queries -WorkbookPrm ($Workbook)`
			  -UpdateExceptionsPrm $UpdateExceptions[$UpdateModePrm]
        }
    	finally{
    		# Reset the original paths in master properties table
    		foreach($CellManipulator in $CellManipulators)
    		{
				$Name = $CellManipulator["Name"]
				$RestoreTo = $CellManipulator["Restore to"]
				$Address = $CellManipulator["Address"]
    			Set-Cell -prmValue $RestoreTo -prmAddress $Address
    			Write-Host "Reset $Name to $RestoreTo"
    		}
    	}
        $Workbook.Save()
    }
    finally{
        if ($Workbook -ne $null){ $Workbook.Close() }
    }
}
###

# Start Excel
$BeforeExcel = Get-Date
$Excel = New-Object -COM "Excel.Application"
$AfterExcel = Get-Date

# Compute full file paths
$RFCFilesPath = (Resolve-Path -Path $RFCFilesPath).Path
$MasterFilepath = (Resolve-Path -Path $MasterFilepath).Path

function Verify-RFCFiles
{
	param($RFCFiles)
	
	# Query all RFC manager names from master workbook
	$MasterWorkbook = $Excel.Workbooks.Open($MasterFilepath)
	$MasterFileProps = Read-MasterFileProperties -MasterWorkbookPrm $MasterWorkbook -UpdateModePrm "Default"
	$ProjectLeads = $MasterFileProps.Leads
    $MasterWorkbook.Close()
    Write-Host "Found $($ProjectLeads.Count) RFC Managers in master"

	Write-Host "Given RFC files: $($RFCFiles.Name)"
	
	foreach($key in $ProjectLeads.Keys)
	{
        $ProjectLeadFilename = Get-ProjectLeadFilename -ProjectPrm $ProjectLeads[$key]
		$FullLeadFilename = "$($ProjectLeadFilename).xlsx"
		Write-Host "Checking for file $FullLeadFilename"
        if ( -not ( $FullLeadFilename -in $RFCFiles.Name) )
		{
			throw "The file $ProjectLeadFilename is missing in the RFC files folder!"
		}
	}
}


try{	
	
	$Excel.Visible = $false
	$Excel.DisplayAlerts = $false

	# Find all RFC manager files in respective folder
	$RFCFiles = Get-ChildItem -Path (Join-Path $RFCFilesPath "*") -Recurse -Include "*.xlsx"

	Verify-RFCFiles -RFCFiles $RFCFiles

	# Query update mode and rfc files selection from user	
	$SelectedUpdateMode	= "Default"
	if (-not $prmScript.IsPresent)
	{
		$RFCFiles = Select-FilesByGUI -CandidateFiles $RFCFiles

		$SelectedUpdateLabels = @{
			"Default (global master >> local master)"="Default";
			"Override (global master >> local master >> local plan)"="Override"
			"Mirror (global master >> local master, global plan >> local plan)"="Mirror"
		}
		$SelectedUpdateLabel = Select-ByGUIWithOptions `
		  -CandidateList $SelectedUpdateLabels.Keys `
		  -OptionsPrm @{"Preselected"="Default (global master >> local master)"}
		$SelectedUpdateMode = $SelectedUpdateLabels[$SelectedUpdateLabel]
	}
	else
	{
		$SelectedUpdateMode	= "Override"
	}
	
    $LoopStart = Get-Date

	$RFCFiles | Foreach-Object -Begin {$i=0}  -Process {
		Update-Workbook -ExcelPrm ([ref]$Excel) -RFCFilePathPrm $_.FullName `
		  -UpdateModePrm $SelectedUpdateMode
			
		$i++
		Write-Host "$i / $($RFCFiles.Count) workbooks updated"
        $Remaining = ( (Get-Date) - $LoopStart).TotalSeconds / $i * ($RFCFiles.Count - $i)
        Write-Host ("{0:f0} seconds remaining  `r`n" -f $Remaining)
	}

}
finally
{
	Write-Host "Tidy Up Started"

	# Properly close excel COM app
	Write-Host "Found $($Excel.Workbooks.Count) open workbooks"
	$Excel.Workbooks | ForEach-Object {$_.Close()}
	$Excel.Quit()

	# Execute garbage collection to ensure correct deallocation of com objects
	[System.GC]::Collect()
	[System.GC]::WaitForPendingFinalizers()

	# Kill the remaining ghost excel process (if it exists)
	Get-Process | Where-Object{
        $_.ProcessName -eq "excel" -and $BeforeExcel -le $_.StartTime -and $_.StartTime -le $AfterExcel} | Stop-Process
	
	Write-Host "Tidy Up Complete"
}

if (-not $prmScript.IsPresent)
{
	Pause
}
